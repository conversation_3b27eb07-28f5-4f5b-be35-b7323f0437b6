import json
import frappe 
from frappe.utils import now_datetime
from healthcare.healthcare.doctype.healthcare_settings.healthcare_settings import get_income_account
from healthcare.healthcare.doctype.sample_collection.sample_collection import create_specimen
from healthcare.healthcare.utils import (
    get_appointment_billing_item_and_rate,
    get_encounters_to_invoice,
    get_lab_tests_to_invoice,
    get_clinical_procedures_to_invoice,
    get_inpatient_services_to_invoice,
    get_therapy_plans_to_invoice,
    get_therapy_sessions_to_invoice,
    get_service_requests_to_invoice,
    get_observations_to_invoice,
    insert_diagnostic_report,
    insert_observation_and_sample_collection,
    manage_doc_for_appointment,
    manage_prescriptions,
    set_invoiced,
    validate_customer_created,
)
from healthcare.healthcare.doctype.lab_test.lab_test import create_multiple


def get_appointments_to_invoice(patient, company):
	appointments_to_invoice = []
	patient_appointments = frappe.get_list(
		"Patient Appointment",
		fields="*",
		filters={
			"patient": patient.name,
            "appointment_for": "Practitioner",
			"company": company,
			"invoiced": 0,
			"status": ["!=", "Cancelled"],
		},
		order_by="appointment_date",
	)

	for appointment in patient_appointments:
		# Procedure Appointments
		if appointment.procedure_template:
			if frappe.db.get_value(
				"Clinical Procedure Template", appointment.procedure_template, "is_billable"
			):
				appointments_to_invoice.append(
					{
						"reference_type": "Patient Appointment",
						"reference_name": appointment.name,
						"service": appointment.procedure_template,
					}
				)
		# Consultation Appointments, should check fee validity
		else:
			if frappe.db.get_single_value(
				"Healthcare Settings", "enable_free_follow_ups"
			) and frappe.db.exists("Fee Validity Reference", {"appointment": appointment.name}):
				continue  # Skip invoicing, fee validty present
			practitioner_charge = 0
			income_account = None
			service_item = None
			if appointment.practitioner:
				details = get_appointment_billing_item_and_rate(appointment)
				service_item = details.get("service_item")
				practitioner_charge = details.get("practitioner_charge")
				income_account = get_income_account(appointment.practitioner, appointment.company)
			appointments_to_invoice.append(
				{
					"reference_type": "Patient Appointment",
					"reference_name": appointment.name,
					"service": service_item,
					"rate": practitioner_charge,
					"income_account": income_account,
				}
			)

	return appointments_to_invoice


@frappe.whitelist()
def get_healthcare_services_to_invoice(patient, customer, company, link_customer=False):
    patient = frappe.get_doc("Patient", patient)
    items_to_invoice = []
    if patient:
        # Customer validated, build a list of billable services
        items_to_invoice += get_registration_to_invoice(patient, company)
        items_to_invoice += get_emergencies_to_invoice(patient, company)
        items_to_invoice += get_appointments_to_invoice(patient, company)
        items_to_invoice += get_encounters_to_invoice(patient, company)
        items_to_invoice += get_lab_tests_to_invoice(patient, company)
        items_to_invoice += get_clinical_procedures_to_invoice(patient, company)
        items_to_invoice += get_inpatient_services_to_invoice(patient, company)
        items_to_invoice += get_therapy_plans_to_invoice(patient, company)
        items_to_invoice += get_therapy_sessions_to_invoice(patient, company)
        items_to_invoice += get_service_requests_to_invoice(patient, company)
        items_to_invoice += get_observations_to_invoice(patient, company)
        validate_customer_created(patient, customer, link_customer)
        return items_to_invoice


def get_registration_to_invoice(patient, company):
    registration_to_invoice = []
    collect_registration_fee = frappe.db.get_single_value(
        "Healthcare Settings", "collect_registration_fee"
    )
    registration_fee = frappe.db.get_single_value(
        "Healthcare Settings", "registration_fee"
    )
    if collect_registration_fee and registration_fee:
        if patient.status == "Unpaid":
            income_account = get_income_account(None, company)
            registration_to_invoice.append(
                {
                    "reference_type": "Patient",
                    "reference_name": patient.name,
                    "service": "Registration Fee",
                    "rate": registration_fee,
                    "income_account": income_account,
                }
            )

    return registration_to_invoice

def get_emergencies_to_invoice(patient, company):
    emergencies_to_invoice = []
    collect_emergency_fee = frappe.db.get_single_value(
        "Opterp Health Settings", "collect_emergency_fee"
    )
    emergency_fee = frappe.db.get_single_value(
        "Opterp Health Settings", "emergency_fee"
    )
    if collect_emergency_fee and emergency_fee:
        income_account = get_income_account(None, company)

        emergencies = frappe.get_all(
            "Emergency",
            filters={
                "patient": patient.name,
                "invoiced": 0,
                "docstatus": ["!=", 2]
            },
            fields=["name"]
        )

        for emergency in emergencies:
            emergencies_to_invoice.append(
                {
                    "reference_type": "Emergency",
                    "reference_name": emergency.name,
                    "service": frappe.db.get_single_value("Opterp Health Settings", "emergency_fee_item"),
                    "rate": emergency_fee,
                    "income_account": income_account,
                }
            )
    return emergencies_to_invoice


def validate_invoiced_on_submit(item):
    if (
        item.reference_dt == "Clinical Procedure"
        and frappe.db.get_single_value(
            "Healthcare Settings", "clinical_procedure_consumable_item"
        )
        == item.item_code
    ):
        is_invoiced = frappe.db.get_value(
            item.reference_dt, item.reference_dn, "consumption_invoiced"
        )

    elif item.reference_dt in ["Service Request", "Medication Request"]:
        billing_status = frappe.db.get_value(
            item.reference_dt, item.reference_dn, "billing_status"
        )
        is_invoiced = True if billing_status == "Invoiced" else False

    elif item.reference_dt == "Patient":
        is_invoiced = (
            True
            if frappe.db.get_value(item.reference_dt, item.reference_dn, "status")
            == "Active"
            else False
        )

    else:
        is_invoiced = frappe.db.get_value(
            item.reference_dt, item.reference_dn, "invoiced"
        )
    if is_invoiced:
        frappe.throw(
            "The item referenced by {0} - {1} is already invoiced.".format(
                item.reference_dt, item.reference_dn
            )
        )


def set_invoiced(item, method, ref_invoice=None):
    invoiced = False
    if method == "on_submit":
        validate_invoiced_on_submit(item)
        invoiced = True

    if item.reference_dt == "Patient":
        frappe.db.set_value(item.reference_dt, item.reference_dn, "status", "Active")

    if item.reference_dt == "Emergency":
        frappe.db.set_value(item.reference_dt, item.reference_dn, "invoiced", 1)

    elif item.reference_dt == "Clinical Procedure":
        service_item = frappe.db.get_single_value(
            "Healthcare Settings", "clinical_procedure_consumable_item"
        )
        if service_item == item.item_code:
            frappe.db.set_value(
                item.reference_dt, item.reference_dn, "consumption_invoiced", invoiced
            )
        else:
            frappe.db.set_value(
                item.reference_dt, item.reference_dn, "invoiced", invoiced
            )
    else:
        if item.reference_dt not in ["Service Request", "Medication Request"]:
            frappe.db.set_value(
                item.reference_dt, item.reference_dn, "invoiced", invoiced
            )

    if item.reference_dt == "Patient Appointment":
        if frappe.db.get_value(
            "Patient Appointment", item.reference_dn, "procedure_template"
        ):
            dt_from_appointment = "Clinical Procedure"
        else:
            dt_from_appointment = "Patient Encounter"
        manage_doc_for_appointment(dt_from_appointment, item.reference_dn, invoiced)

    elif item.reference_dt == "Lab Prescription":
        manage_prescriptions(
            invoiced,
            item.reference_dt,
            item.reference_dn,
            "Lab Test",
            "lab_test_created",
        )

    elif item.reference_dt == "Procedure Prescription":
        manage_prescriptions(
            invoiced,
            item.reference_dt,
            item.reference_dn,
            "Clinical Procedure",
            "procedure_created",
        )
    elif item.reference_dt in ["Service Request", "Medication Request"]:
        # if order is invoiced, set both order and service transaction as invoiced
        hso = frappe.get_doc(item.reference_dt, item.reference_dn)
        if invoiced:
            hso.update_invoice_details(item.qty)
        else:
            hso.update_invoice_details(item.qty * -1)

        # service transaction linking to HSO
        if item.reference_dt == "Service Request":
            template_map = {
                "Clinical Procedure Template": "Clinical Procedure",
                "Therapy Type": "Therapy Session",
                "Lab Test Template": "Lab Test",
                # 'Healthcare Service Unit': 'Inpatient Occupancy'
            }


def manage_invoice_submit_cancel(doc, method):
    if not doc.patient:
        return

    if doc.items:
        for item in doc.items:
            if item.get("reference_dt") and item.get("reference_dn"):
                # TODO check
                # if frappe.get_meta(item.reference_dt).has_field("invoiced"):
                set_invoiced(item, method, doc.name)

        if method == "on_submit" and frappe.db.get_single_value(
            "Healthcare Settings", "create_observation_on_si_submit"
        ):
            create_sample_collection_and_observation(doc)

    if method == "on_submit":
        if frappe.db.get_single_value(
            "Healthcare Settings", "create_lab_test_on_si_submit"
        ):  
            if doc == "Sales Invoice":
                create_multiple("Sales Invoice", doc.name)
            elif doc == "POS Invoice":
                create_multiple("POS Invoice", doc.name)

        if (
            not frappe.db.get_single_value("Healthcare Settings", "show_payment_popup")
            and frappe.db.get_single_value(
                "Healthcare Settings", "enable_free_follow_ups"
            )
            and doc.items
        ):
            for item in doc.items:
                if item.reference_dt == "Patient Appointment":
                    fee_validity = frappe.db.exists(
                        "Fee Validity", {"patient_appointment": item.reference_dn}
                    )
                    if fee_validity:
                        frappe.db.set_value(
                            "Fee Validity", fee_validity, "sales_invoice_ref", doc.name
                        )


def manage_invoice_validate(doc, method):
    if doc.service_unit and len(doc.items):
        for item in doc.items:
            if not item.service_unit:
                item.service_unit = doc.service_unit


def create_sample_collection(doc, patient):
    patient = frappe.get_doc("Patient", patient)
    sample_collection = frappe.new_doc("Sample Collection")
    sample_collection.patient = patient.name
    sample_collection.patient_age = patient.get_age()
    sample_collection.patient_sex = patient.sex
    sample_collection.company = doc.company
    sample_collection.referring_practitioner = doc.ref_practitioner
    sample_collection.reference_doc = doc.doctype
    sample_collection.reference_name = doc.name
    if doc.doctype in ["Sales Invoice", "POS Invoice"]:
        sample_collection.invoiced = 1
    else:
        sample_collection.invoiced = doc.invoiced
    return sample_collection


def create_sample_collection_and_observation(doc):
    if doc == "Sales Invoice":
        meta = frappe.get_meta("Sales Invoice Item", cached=True)
    else:
        meta = frappe.get_meta("POS Invoice Item", cached=True)
    diag_report_required = False
    data = []
    for item in doc.items:
        # to set patient in item table if not set
        if meta.has_field("patient") and not item.patient:
            item.patient = doc.patient

        # ignore if already created from service request
        if item.get("reference_dt") == "Service Request" and item.get("reference_dn"):
            if frappe.db.exists(
                "Observation Sample Collection",
                {"service_request": item.get("reference_dn")},
            ) or frappe.db.exists(
                "Sample Collection", {"service_request": item.get("reference_dn")}
            ):
                continue

        template_id = frappe.db.exists("Observation Template", {"item": item.item_code})
        if template_id:
            temp_dict = {}
            temp_dict["name"] = template_id
            if meta.has_field("patient") and item.get("patient"):
                temp_dict["patient"] = item.get("patient")
                temp_dict["child"] = item.get("name")
            data.append(temp_dict)

    out_data = []
    for d in data:
        observation_template = frappe.get_value(
            "Observation Template",
            d.get("name"),
            [
                "sample_type",
                "sample",
                "medical_department",
                "container_closure_color",
                "name",
                "sample_qty",
                "has_component",
                "sample_collection_required",
            ],
            as_dict=True,
        )
        if observation_template:
            observation_template["patient"] = d.get("patient")
            observation_template["child"] = d.get("child")
            out_data.append(observation_template)
    if not meta.has_field("patient"):
        sample_collection = create_sample_collection(doc, doc.patient)
    else:
        grouped = {}
        for grp in out_data:
            grouped.setdefault(grp.patient, []).append(grp)
        if grouped:
            out_data = grouped

    for grp in out_data:
        patient = doc.patient
        if meta.has_field("patient") and grp:
            patient = grp
        if meta.has_field("patient"):
            sample_collection = create_sample_collection(doc, patient)
            for obs in out_data[grp]:
                (
                    sample_collection,
                    diag_report_required,
                ) = insert_observation_and_sample_collection(
                    doc, patient, obs, sample_collection, obs.get("child")
                )
            if (
                sample_collection
                and len(sample_collection.get("observation_sample_collection")) > 0
            ):
                sample_collection.save(ignore_permissions=True)

            if diag_report_required:
                insert_diagnostic_report(doc, patient, sample_collection.name)
        else:
            sample_collection, diag_report_required = (
                insert_observation_and_sample_collection(
                    doc, patient, grp, sample_collection
                )
            )

    if not meta.has_field("patient"):
        if (
            sample_collection
            and len(sample_collection.get("observation_sample_collection")) > 0
        ):
            sample_collection.save(ignore_permissions=True)

        if diag_report_required:
            insert_diagnostic_report(doc, patient, sample_collection.name)


@frappe.whitelist()
def create_observation(
    selected, sample_collection, component_observations=None, child_name=None
):
    frappe.enqueue(
        "opterp_health.opterp_health.utils.insert_observation",
        selected=selected,
        sample_collection=sample_collection,
        component_observations=component_observations,
        child_name=child_name,
    )


def insert_observation(
    selected, sample_collection, component_observations=None, child_name=None
):
    try:
        sample_col_doc = frappe.db.get_value(
            "Sample Collection",
            sample_collection,
            [
                "reference_doc",
                "reference_name",
                "patient",
                "referring_practitioner",
                "invoiced",
            ],
            as_dict=1,
        )
        selected = json.loads(selected)
        if component_observations and len(component_observations) > 0:
            component_observations = json.loads(component_observations)
        comp_obs_ref = create_specimen(
            sample_col_doc.get("patient"), selected, component_observations
        )
        for i, obs in enumerate(selected):
            parent_observation = obs.get("component_observation_parent")

            if child_name:
                parent_observation = frappe.db.get_value(
                    "Observation Sample Collection",
                    child_name,
                    "component_observation_parent",
                )

            if obs.get("status") == "Open":
                # non has_component templates
                if not obs.get("has_component") or obs.get("has_component") == 0:
                    observation = add_observation(
                        patient=sample_col_doc.get("patient"),
                        template=obs.get("observation_template"),
                        doc="Sample Collection",
                        docname=sample_collection,
                        parent=parent_observation,
                        specimen=comp_obs_ref.get(obs.get("name"))
                        or comp_obs_ref.get(i + 1)
                        or comp_obs_ref.get(obs.get("idx")),
                        invoice_type=sample_col_doc.get("reference_doc"),
                        invoice=sample_col_doc.get("reference_name"),
                        invoiced=sample_col_doc.get("invoiced"),
                        practitioner=sample_col_doc.get("referring_practitioner"),
                        child=(
                            obs.get("reference_child")
                            if obs.get("reference_child")
                            else ""
                        ),
                        service_request=obs.get("service_request"),
                    )
                    if observation:
                        frappe.db.set_value(
                            "Observation Sample Collection",
                            obs.get("name"),
                            {
                                "status": "Collected",
                                "collection_date_time": now_datetime(),
                                "specimen": comp_obs_ref.get(obs.get("name")),
                            },
                        )
                else:
                    # to deal the component template checked from main table and collected
                    if obs.get("component_observations"):
                        component_observations = json.loads(
                            obs.get("component_observations")
                        )
                        for j, comp in enumerate(component_observations):
                            observation = add_observation(
                                patient=sample_col_doc.get("patient"),
                                template=comp.get("observation_template"),
                                doc="Sample Collection",
                                docname=sample_collection,
                                parent=obs.get("component_observation_parent"),
                                specimen=comp_obs_ref.get(j + 1)
                                or comp_obs_ref.get(obs.get("name")),
                                invoice=sample_col_doc.get("reference_name"),
                                invoiced=sample_col_doc.get("invoiced"),
                                practitioner=sample_col_doc.get(
                                    "referring_practitioner"
                                ),
                                child=(
                                    obs.get("reference_child")
                                    if obs.get("reference_child")
                                    else ""
                                ),
                                service_request=obs.get("service_request"),
                            )
                            if observation:
                                comp["status"] = "Collected"
                                comp["collection_date_time"] = now_datetime()
                                comp["specimen"] = comp_obs_ref.get(
                                    j + 1
                                ) or comp_obs_ref.get(obs.get("name"))

                        frappe.db.set_value(
                            "Observation Sample Collection",
                            obs.get("name"),
                            {
                                "collection_date_time": now_datetime(),
                                "component_observations": json.dumps(
                                    component_observations, default=str
                                ),
                                "status": "Collected",
                                "specimen": comp_obs_ref.get(j + 1)
                                or comp_obs_ref.get(obs.get("name")),
                            },
                        )
            # to deal individually checked from component dialog
            if component_observations:
                for j, comp in enumerate(component_observations):
                    if comp.get("observation_template") == obs.get(
                        "observation_template"
                    ):
                        comp["status"] = "Collected"
                        comp["collection_date_time"] = now_datetime()
                        comp["specimen"] = comp_obs_ref.get(j + 1)

        child_db_set_dict = {
            "component_observations": json.dumps(component_observations, default=str)
        }
        # to set child table status Collected if all childs are Collected
        if component_observations and not any(
            (comp["status"] == "Open") for comp in component_observations
        ):
            child_db_set_dict["status"] = "Collected"

        if child_name:
            frappe.db.set_value(
                "Observation Sample Collection",
                child_name,
                child_db_set_dict,
            )
        if sample_collection:
            non_collected_samples = frappe.db.get_all(
                "Observation Sample Collection",
                {"parent": sample_collection, "status": ["!=", "Collected"]},
            )
            if non_collected_samples and len(non_collected_samples) > 0:
                set_status = "Partly Collected"
            else:
                set_status = "Collected"

            frappe.db.set_value(
                "Sample Collection", sample_collection, "status", set_status
            )

    except Exception as e:
        frappe.log_error(message=e, title="Failed to mark Collected!")

    frappe.publish_realtime(
        event="observation_creation_progress",
        message="Completed",
        doctype="Sample Collection",
        docname=sample_collection,
    )


@frappe.whitelist()
def add_observation(**args):
    observation_doc = frappe.new_doc("Observation")
    observation_doc.posting_datetime = now_datetime()
    observation_doc.patient = args.get("patient")
    observation_doc.observation_template = args.get("template")
    observation_doc.permitted_data_type = args.get("data_type")
    observation_doc.reference_doctype = args.get("doc")
    observation_doc.reference_docname = args.get("docname")
    if args.get("invoice_type") == "Sales Invoice":
        observation_doc.sales_invoice = args.get("invoice")
    elif args.get("invoice_type") == "POS Invoice":
        observation_doc.pos_invoice = args.get("invoice")
    observation_doc.healthcare_practitioner = args.get("practitioner")
    observation_doc.invoiced = args.get("invoiced")
    observation_doc.specimen = args.get("specimen")
    if args.get("data_type") in ["Range", "Ratio", "Quantity", "Numeric"]:
        observation_doc.result_data = args.get("result")
    # elif data_type in ["Quantity", "Numeric"]:
    # 	observation_doc.result_float = result
    elif args.get("data_type") == "Text":
        observation_doc.result_text = args.get("result")
    if args.get("parent"):
        observation_doc.parent_observation = args.get("parent")
    observation_doc.sales_invoice_item = args.get("child") if args.get("child") else ""
    observation_doc.insert(ignore_permissions=True)
    return observation_doc.name


@frappe.whitelist()
def get_drugs_to_invoice(encounter, customer, link_customer=False):
    encounter = frappe.get_doc("Patient Encounter", encounter)
    if link_customer:
        frappe.db.set_value("Patient", encounter.patient, "customer", customer)
    if encounter:
        patient = frappe.get_doc("Patient", encounter.patient)
        if patient:
            orders_to_invoice = []
            medication_requests = frappe.get_list(
                "Medication Request",
                fields=["*"],
                filters={
                    "patient": patient.name,
                    "order_group": encounter.name,
                    "billing_status": ["in", ["Pending", "Partly Invoiced"]],
                    "docstatus": 1,
                },
            )
            for medication_request in medication_requests:
                if medication_request.medication:
                    is_billable = frappe.get_cached_value(
                        "Medication", medication_request.medication, ["is_billable"]
                    )
                else:
                    is_billable = frappe.db.exists(
                        "Item",
                        {"name": medication_request.medication_item, "disabled": False},
                    )

                description = ""
                if medication_request.dosage and medication_request.period:
                    description = _("{0} for {1}").format(
                        medication_request.dosage, medication_request.period
                    )

                if medication_request.medication_item and is_billable:
                    billable_order_qty = medication_request.get(
                        "quantity", 1
                    ) - medication_request.get("qty_invoiced", 0)
                    if medication_request.number_of_repeats_allowed:
                        if (
                            medication_request.total_dispensable_quantity
                            >= medication_request.quantity
                            + medication_request.qty_invoiced
                        ):
                            billable_order_qty = medication_request.get("quantity", 1)
                        else:
                            billable_order_qty = (
                                medication_request.total_dispensable_quantity
                                - medication_request.get("qty_invoiced", 0)
                            )

                    orders_to_invoice.append(
                        {
                            "reference_type": "Medication Request",
                            "reference_name": medication_request.name,
                            "drug_code": medication_request.medication_item,
                            "quantity": billable_order_qty,
                            "description": description,
                        }
                    )
            return orders_to_invoice
