import frappe
from healthcare.healthcare.doctype.sample_collection.sample_collection import <PERSON><PERSON><PERSON>ollection as OldSampleCollection
import json

class SampleCollection(OldSampleCollection):
    def validate(self):
        if self.observation_sample_collection:
            for obs in self.observation_sample_collection:
                if obs.get("has_component") and obs.get("component_observations"):
                    component_observations = json.loads(
                        obs.get("component_observations")
                    )
                    if not any(
                        (comp["status"] == "Open") for comp in component_observations
                    ):
                        obs.status = "Collected"

        if not any(
            (obs.get("status") == "Open") for obs in self.observation_sample_collection
        ):
            self.status = "Collected"
        elif any(
            (obs.get("status") == "Collected")
            for obs in self.observation_sample_collection
        ):
            self.status = "Partly Collected"
        else:
            self.status = "Pending"

    def before_submit(self):
        if self.observation_sample_collection:
            for obs in self.observation_sample_collection:
                if obs.get("status") == "Open":
                    frappe.throw("Cannot submit, some samples are not collected")
