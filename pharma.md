1. Add MRP to Batch  
Go to Customize Form → Batch.  
Add a Custom Field:  
Field Label: MRP  
Field Type: Currency  
Options: currency

Mandatory: Yes (optional, but recommended)

This ensures every batch has its own MRP.

2. Set MRP at Purchase
When you receive medicines in Purchase Receipt or Purchase Invoice:

A batch is created for each lot.

Enter the MRP printed on the strip/box in the MRP field of the batch.

3. Auto-fetch MRP in Sales
Add a Custom Field MRP to Sales Invoice Item.

Create a Client Script to fetch MRP from the batch when you select it:

```javascript
frappe.ui.form.on('Sales Invoice Item', {
    batch_no: function(frm, cdt, cdn) {
        let row = locals[cdt][cdn];
        if(row.batch_no) {
            frappe.db.get_value('Batch', row.batch_no, 'mrp').then(r => {
                if(r.message.mrp) {
                    frappe.model.set_value(cdt, cdn, 'mrp', r.message.mrp);
                }
            });
        }
    }
});
```

4. Prevent Selling Above MRP
Add another check in the same script:

```javascript
frappe.ui.form.on('Sales Invoice Item', {
    rate: function(frm, cdt, cdn) {
        let row = locals[cdt][cdn];
        if(row.rate > row.mrp) {
            frappe.throw(`Selling Rate cannot exceed MRP (${row.mrp})`);
        }
    }
});
```