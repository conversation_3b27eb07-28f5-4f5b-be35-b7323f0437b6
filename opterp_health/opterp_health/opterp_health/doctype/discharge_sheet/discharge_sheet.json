{"actions": [], "allow_rename": 1, "autoname": "format:DS-{inpatient_record}", "creation": "2025-07-31 14:19:40.810847", "doctype": "DocType", "engine": "InnoDB", "field_order": ["patient", "inpatient_record", "department", "discharge_type", "column_break_mrub", "date_of_admission", "date_of_discharge", "primary_practitioner", "stay_in_icu", "days_in_icu", "section_break_uzbq", "provisional_diagnosis", "column_break_lusg", "final_diagnosis", "section_break_uipw", "chief_complaint", "personal_history", "drug_and_allergy", "course_of_hospital_stay", "advice", "column_break_jwef", "history_of_present_<PERSON><PERSON><PERSON>", "history_of_past_illness", "general_examination", "append_last_taken_vitals", "systemic_examination", "medication_during_discharge", "add_ot_procedure", "ot_procedure", "section_break_ppup", "add_menstrual_history", "menstrual_history", "column_break_qptm", "add_obstetric_history", "obstetric_history", "add_baby_details", "baby_details", "section_break_ykhi", "add_birth_history", "birth_history", "add_developmental_history", "developmental_history", "add_immunization_history", "immunization_history", "section_break_vphk", "relevant_investigations", "follow_up", "signatures_section", "signatories", "amended_from"], "fields": [{"fieldname": "chief_complaint", "fieldtype": "Text Editor", "label": "Chief <PERSON><PERSON><PERSON><PERSON>"}, {"fieldname": "history_of_present_<PERSON><PERSON><PERSON>", "fieldtype": "Text Editor", "label": "History of Present Illness(HOPI)"}, {"fieldname": "personal_history", "fieldtype": "Text Editor", "label": "Personal History"}, {"fieldname": "drug_and_allergy", "fieldtype": "Text Editor", "label": "Drug and Allergy"}, {"fieldname": "general_examination", "fieldtype": "Text Editor", "label": "General Examination"}, {"fieldname": "systemic_examination", "fieldtype": "Text Editor", "label": "Systemic Examination"}, {"fieldname": "medication_during_discharge", "fieldtype": "Text Editor", "label": " Medication During Discharge "}, {"fieldname": "follow_up", "fieldtype": "Small Text", "label": " Follow Up "}, {"fieldname": "column_break_jwef", "fieldtype": "Column Break"}, {"fieldname": "discharge_type", "fieldtype": "Select", "in_list_view": 1, "label": "Discharge Type", "options": "Discharge\nLAMA\nDOPR\nRefer\nDeath"}, {"fetch_from": "inpatient_record.patient", "fieldname": "patient", "fieldtype": "Link", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Patient", "options": "Patient", "read_only": 1}, {"fieldname": "inpatient_record", "fieldtype": "Link", "label": "Inpatient Record", "options": "Inpatient Record", "unique": 1}, {"fieldname": "section_break_uipw", "fieldtype": "Section Break"}, {"fieldname": "column_break_mrub", "fieldtype": "Column Break"}, {"fetch_from": "inpatient_record.admitted_datetime", "fieldname": "date_of_admission", "fieldtype": "Date", "in_filter": 1, "in_list_view": 1, "in_standard_filter": 1, "label": "Date of Admission"}, {"fieldname": "date_of_discharge", "fieldtype": "Date", "in_list_view": 1, "in_standard_filter": 1, "label": "Date of Discharge"}, {"fieldname": "primary_practitioner", "fieldtype": "Link", "in_list_view": 1, "in_standard_filter": 1, "label": "Primary Practitioner", "options": "Healthcare Practitioner"}, {"fieldname": "section_break_uzbq", "fieldtype": "Section Break"}, {"fieldname": "final_diagnosis", "fieldtype": "Text Editor", "label": "Final Diagnosis"}, {"fieldname": "section_break_vphk", "fieldtype": "Section Break"}, {"fieldname": "relevant_investigations", "fieldtype": "Text Editor", "label": "Relevant Investigations"}, {"fieldname": "signatures_section", "fieldtype": "Section Break", "label": "Signatures"}, {"fieldname": "signatories", "fieldtype": "Table", "label": "Signatories", "options": "Discharge Signatories"}, {"fieldname": "department", "fieldtype": "Link", "label": "Department", "options": "Medical Department"}, {"default": "0", "fieldname": "stay_in_icu", "fieldtype": "Check", "label": "Stay in ICU"}, {"depends_on": "eval: doc.stay_in_icu", "fieldname": "days_in_icu", "fieldtype": "Data", "label": "Days in ICU"}, {"fieldname": "course_of_hospital_stay", "fieldtype": "Text Editor", "label": "Medication during Hospital Stay "}, {"fieldname": "column_break_lusg", "fieldtype": "Column Break"}, {"fieldname": "provisional_diagnosis", "fieldtype": "Text Editor", "label": "Provisional Diagnosis"}, {"fieldname": "advice", "fieldtype": "Text Editor", "label": "Advice"}, {"fieldname": "amended_from", "fieldtype": "Link", "label": "Amended From", "no_copy": 1, "options": "Discharge Sheet", "print_hide": 1, "read_only": 1, "search_index": 1}, {"fieldname": "append_last_taken_vitals", "fieldtype": "<PERSON><PERSON>", "label": "Append Last taken <PERSON><PERSON>"}, {"fieldname": "history_of_past_illness", "fieldtype": "Text Editor", "label": "History of Past Illness"}, {"depends_on": "eval: doc.add_ot_procedure", "fieldname": "ot_procedure", "fieldtype": "Text Editor", "label": "OT Procedure"}, {"fieldname": "section_break_ppup", "fieldtype": "Section Break", "label": "GYNE"}, {"default": "0", "fieldname": "add_birth_history", "fieldtype": "Check", "label": "Add birth History"}, {"depends_on": "eval: doc.add_birth_history", "fieldname": "birth_history", "fieldtype": "Text Editor", "label": "Birth History"}, {"fieldname": "column_break_qptm", "fieldtype": "Column Break"}, {"default": "0", "fieldname": "add_developmental_history", "fieldtype": "Check", "label": "Add Developmental History"}, {"depends_on": "eval: doc.add_developmental_history", "fieldname": "developmental_history", "fieldtype": "Text Editor", "label": "Developmental History"}, {"default": "0", "fieldname": "add_immunization_history", "fieldtype": "Check", "label": "Add Immunization History"}, {"depends_on": "eval: doc.add_immunization_history", "fieldname": "immunization_history", "fieldtype": "Text Editor", "label": "Immunization History"}, {"fieldname": "section_break_ykhi", "fieldtype": "Section Break", "label": "PEDIATRICS"}, {"default": "0", "fieldname": "add_menstrual_history", "fieldtype": "Check", "label": "Add Menstrual History"}, {"depends_on": "eval: doc.add_menstrual_history", "fieldname": "menstrual_history", "fieldtype": "Text Editor", "label": "Menstrual History"}, {"depends_on": "eval: doc.add_obstetric_history", "fieldname": "obstetric_history", "fieldtype": "Text Editor", "label": "Obstetric History"}, {"default": "0", "fieldname": "add_baby_details", "fieldtype": "Check", "label": "Add Baby Details"}, {"depends_on": "eval: doc.add_baby_details", "fieldname": "baby_details", "fieldtype": "Text Editor", "label": "Baby Details"}, {"default": "0", "fieldname": "add_obstetric_history", "fieldtype": "Check", "label": "Add  Obstetric History"}, {"default": "0", "fieldname": "add_ot_procedure", "fieldtype": "Check", "label": "Add OT Procedure"}], "hide_toolbar": 1, "index_web_pages_for_search": 1, "is_submittable": 1, "links": [], "modified": "2025-08-12 11:00:32.847101", "modified_by": "Administrator", "module": "Opterp Health", "name": "Discharge Sheet", "naming_rule": "Expression", "owner": "Administrator", "permissions": [{"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "System Manager", "share": 1, "write": 1}, {"create": 1, "delete": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Physician", "share": 1, "submit": 1, "write": 1}, {"create": 1, "email": 1, "export": 1, "print": 1, "read": 1, "report": 1, "role": "Nursing User", "share": 1, "write": 1}], "sort_field": "modified", "sort_order": "DESC", "states": []}